package util

import (
	"github.com/pkg/errors"

	amlPb "github.com/epifi/gamma/api/aml"
)

func GetReviewStatusForCaseDecision(caseDecision amlPb.CaseDecision) (amlPb.ReviewStatus, error) {
	switch caseDecision {
	case amlPb.CaseDecision_CASE_DECISION_APPROVED:
		return amlPb.ReviewStatus_REVIEW_STATUS_APPROVED, nil
	case amlPb.CaseDecision_CASE_DECISION_REJECTED:
		return amlPb.ReviewStatus_REVIEW_STATUS_REJECTED, nil
	default:
		return 0, errors.Errorf("unexpected case decision: %v", caseDecision)
	}
}
