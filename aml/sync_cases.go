package aml

import (
	"context"

	"github.com/pkg/errors"
	zap "go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	amlPb "github.com/epifi/gamma/api/aml"
	amlVgPb "github.com/epifi/gamma/api/vendorgateway/aml"
	vmPb "github.com/epifi/gamma/api/vendormapping"
)

func (s *Service) SyncCases(ctx context.Context, req *amlPb.SyncCasesRequest) (*amlPb.SyncCasesResponse, error) {
	err := s.syncCases(ctx, req)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &amlPb.SyncCasesResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		logger.Error(ctx, "error in syncCases", zap.Error(err))
		return &amlPb.SyncCasesResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &amlPb.SyncCasesResponse{Status: rpcPb.StatusOk()}, nil
}

func (s *Service) syncCases(ctx context.Context, req *amlPb.SyncCasesRequest) error {
	// Set default case categories if not provided (all except unspecified)
	caseCategories := req.GetCaseCategories()
	if len(caseCategories) == 0 {
		for i := range amlVgPb.CaseCategory_name {
			if i == 0 {
				continue
			}
			caseCategories = append(caseCategories, amlVgPb.CaseCategory(i))
		}
	}

	// Set default case types if not provided (all except unspecified)
	caseTypes := req.GetCaseTypes()
	if len(caseTypes) == 0 {
		for i := range amlVgPb.CaseType_name {
			if i == 0 {
				continue
			}
			caseTypes = append(caseTypes, amlVgPb.CaseType(i))
		}
	}

	res, err := s.amlVgClient.ListCases(ctx, &amlVgPb.ListCasesRequest{
		Header:         &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_TSS},
		Owner:          req.GetOwner(),
		CaseCategories: caseCategories,
		FromDateTime:   req.GetFromDateTime(),
		ToDateTime:     req.GetToDateTime(),
		CaseTypes:      caseTypes,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsRecordNotFound() {
			return errors.Wrap(epifierrors.ErrRecordNotFound, "no cases found from vendor")
		}
		return errors.Wrap(err, "error getting cases from vendor")
	}

	for _, vgCase := range res.GetCases() {
		err = s.upsertCase(ctx, vgCase, req.GetOwner())
		if err != nil {
			return errors.Wrap(err, "error upserting case")
		}
	}
	return nil
}

func (s *Service) upsertCase(ctx context.Context, vgCase *amlVgPb.Case, owner common.Owner) error {
	vmRes, err := s.vmClient.GetInputIdByVendor(ctx, &vmPb.GetInputIdByVendorRequest{
		Id:     vgCase.GetSourceSystemCustomerCode(),
		Vendor: commonvgpb.Vendor_TSS,
	})
	if err = epifigrpc.RPCError(vmRes, err); err != nil {
		return errors.Wrap(err, "error getting actor id from vendor id")
	}
	// Check if case already exists
	existingCase, err := s.amlCaseDetailsDao.GetByVendorCaseId(ctx, vgCase.GetCaseId(), owner)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return errors.Wrap(err, "error getting existing case")
	}
	if existingCase == nil {
		// Create new case
		caseDetails := &amlPb.AmlCaseDetails{
			ActorId:      vmRes.GetInputId(),
			VendorCaseId: vgCase.GetCaseId(),
			Vendor:       commonvgpb.Vendor_TSS,
			Owner:        owner,
		}
		_, err = s.amlCaseDetailsDao.Create(ctx, caseDetails)
		if err != nil {
			return errors.Wrap(err, "error in creating case details")
		}
		return nil
	}
	// TODO(Brijesh): Get clarity on how to know if case is approved or rejected based on vendor case details
	return nil
}
