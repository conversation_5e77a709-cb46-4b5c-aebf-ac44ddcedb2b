package consumer

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/aml/config"
	"github.com/epifi/gamma/aml/dao"
	"github.com/epifi/gamma/aml/util"
	amlPb "github.com/epifi/gamma/api/aml"
	vmPb "github.com/epifi/gamma/api/vendormapping"
	ownerPkg "github.com/epifi/gamma/pkg/owner"
)

type Service struct {
	conf                      *config.Config
	amlCaseDetailsDao         dao.AmlCaseDetailsDao
	vmClient                  vmPb.VendorMappingServiceClient
	fileGeneratorFactory      *FileGeneratorFactory
	fileGenerationAttemptsDao dao.AmlFileGenerationAttemptsDao
	txnExecutorProvider       *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]
}

func NewService(
	conf *config.Config,
	amlCaseDetailsDao dao.AmlCaseDetailsDao,
	vmClient vmPb.VendorMappingServiceClient,
	fileGenerationFactory *FileGeneratorFactory,
	fileGenerationAttemptsDao dao.AmlFileGenerationAttemptsDao,
	txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
) *Service {
	return &Service{
		conf:                      conf,
		amlCaseDetailsDao:         amlCaseDetailsDao,
		vmClient:                  vmClient,
		fileGeneratorFactory:      fileGenerationFactory,
		fileGenerationAttemptsDao: fileGenerationAttemptsDao,
		txnExecutorProvider:       txnExecutorProvider,
	}
}

func (s *Service) ProcessCallbackForDecisionsOnCase(ctx context.Context, req *amlPb.ProcessCallbackForDecisionsOnCaseRequest) (*amlPb.ProcessCallbackForDecisionsOnCaseResponse, error) {
	// update the case decision status in db
	for _, decision := range req.GetDecisionDetails() {
		ownership, err := ownerPkg.GetOwnershipFromOwner(req.GetOwner())
		if err != nil {
			logger.Error(ctx, "error getting ownership from owner", zap.Error(err))
			return &amlPb.ProcessCallbackForDecisionsOnCaseResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
		}
		txnExecutor, txnExecErr := s.txnExecutorProvider.GetResourceForOwnership(ownership)
		if txnExecErr != nil {
			logger.Error(ctx, "error getting txn executor for epifi tech", zap.Error(txnExecErr))
			return &amlPb.ProcessCallbackForDecisionsOnCaseResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
		}
		txnErr := txnExecutor.RunTxn(ctx, func(ctx context.Context) error {
			caseDetails, caseErr := s.amlCaseDetailsDao.GetByVendorCaseId(ctx, decision.GetVendorCaseId(), req.GetOwner())
			if caseErr != nil {
				if errors.Is(caseErr, epifierrors.ErrRecordNotFound) {
					// this can happen when the case is created during continuous screening process which is a daily job run on the vendor application
					// this should not happen if the case is created during initial screening with API
					logger.Info(ctx, "case id not found in case details table", zap.String(util.VendorCaseIdStr, decision.GetVendorCaseId()))
					// create record in case details table when not found
					createErr := s.createCaseDetailsRecord(ctx, req.GetOwner(), decision, req.GetVendorName())
					if createErr != nil {
						return createErr
					}
					return nil
				}
				return errors.Wrap(caseErr, "error in getting case details")
			}
			reviewStatus, cErr := util.GetReviewStatusForCaseDecision(decision.GetCaseDecision())
			if cErr != nil {
				return errors.Wrapf(cErr, "error getting review status for case decision: %v", decision.GetCaseDecision())
			}
			caseDetails.ReviewStatus = reviewStatus
			caseDetails.Owner = req.GetOwner()
			upErr := s.amlCaseDetailsDao.Update(ctx, caseDetails, []amlPb.AmlCaseDetailsFieldMask{amlPb.AmlCaseDetailsFieldMask_AML_CASE_DETAILS_FIELD_MASK_REVIEW_STATUS})
			if upErr != nil {
				return errors.Wrap(upErr, "error in updating review status in case details table")
			}

			return nil
		})
		if txnErr != nil {
			logger.Error(ctx, "error in transaction for updating case details", zap.Error(txnErr), zap.String(util.VendorCaseIdStr, decision.GetVendorCaseId()))
			return &amlPb.ProcessCallbackForDecisionsOnCaseResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
		}
	}
	return &amlPb.ProcessCallbackForDecisionsOnCaseResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}, nil
}

func (s *Service) createCaseDetailsRecord(ctx context.Context,
	owner common.Owner, decisionDetails *amlPb.DecisionDetails, vendor commonvgpb.Vendor) error {
	vmRes, vmErr := s.vmClient.GetInputIdByVendor(ctx, &vmPb.GetInputIdByVendorRequest{
		Id:     decisionDetails.GetRecordIdentifier(),
		Vendor: vendor,
	})
	if te := epifigrpc.RPCError(vmRes, vmErr); te != nil {
		logger.Error(ctx, "error in getting actor id from vendor id", zap.Error(te),
			zap.String(logger.VENDOR_ID, decisionDetails.GetRecordIdentifier()), zap.String(util.VendorCaseIdStr, decisionDetails.GetVendorCaseId()))
		return errors.Wrap(te, "error in getting actor id from vendor id")
	}
	reviewStatus, err := util.GetReviewStatusForCaseDecision(decisionDetails.GetCaseDecision())
	if err != nil {
		return errors.Wrapf(err, "error getting review status for case decision: %v", decisionDetails.GetCaseDecision())
	}
	_, cErr := s.amlCaseDetailsDao.Create(ctx, &amlPb.AmlCaseDetails{
		ActorId:      vmRes.GetInputId(),
		ReviewStatus: reviewStatus,
		VendorCaseId: decisionDetails.GetVendorCaseId(),
		Vendor:       vendor,
		Owner:        owner,
	})
	if cErr != nil {
		return errors.Wrap(cErr, "error in creating case details")
	}
	return nil
}
