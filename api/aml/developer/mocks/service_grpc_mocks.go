// Code generated by MockGen. DO NOT EDIT.
// Source: api/aml/developer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAmlDevServiceClient is a mock of AmlDevServiceClient interface.
type MockAmlDevServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockAmlDevServiceClientMockRecorder
}

// MockAmlDevServiceClientMockRecorder is the mock recorder for MockAmlDevServiceClient.
type MockAmlDevServiceClientMockRecorder struct {
	mock *MockAmlDevServiceClient
}

// NewMockAmlDevServiceClient creates a new mock instance.
func NewMockAmlDevServiceClient(ctrl *gomock.Controller) *MockAmlDevServiceClient {
	mock := &MockAmlDevServiceClient{ctrl: ctrl}
	mock.recorder = &MockAmlDevServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAmlDevServiceClient) EXPECT() *MockAmlDevServiceClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockAmlDevServiceClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockAmlDevServiceClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockAmlDevServiceClient)(nil).GetData), varargs...)
}

// GetEntityList mocks base method.
func (m *MockAmlDevServiceClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityList", varargs...)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockAmlDevServiceClientMockRecorder) GetEntityList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockAmlDevServiceClient)(nil).GetEntityList), varargs...)
}

// GetParameterList mocks base method.
func (m *MockAmlDevServiceClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterList", varargs...)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockAmlDevServiceClientMockRecorder) GetParameterList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockAmlDevServiceClient)(nil).GetParameterList), varargs...)
}

// MockAmlDevServiceServer is a mock of AmlDevServiceServer interface.
type MockAmlDevServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockAmlDevServiceServerMockRecorder
}

// MockAmlDevServiceServerMockRecorder is the mock recorder for MockAmlDevServiceServer.
type MockAmlDevServiceServerMockRecorder struct {
	mock *MockAmlDevServiceServer
}

// NewMockAmlDevServiceServer creates a new mock instance.
func NewMockAmlDevServiceServer(ctrl *gomock.Controller) *MockAmlDevServiceServer {
	mock := &MockAmlDevServiceServer{ctrl: ctrl}
	mock.recorder = &MockAmlDevServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAmlDevServiceServer) EXPECT() *MockAmlDevServiceServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockAmlDevServiceServer) GetData(arg0 context.Context, arg1 *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockAmlDevServiceServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockAmlDevServiceServer)(nil).GetData), arg0, arg1)
}

// GetEntityList mocks base method.
func (m *MockAmlDevServiceServer) GetEntityList(arg0 context.Context, arg1 *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockAmlDevServiceServerMockRecorder) GetEntityList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockAmlDevServiceServer)(nil).GetEntityList), arg0, arg1)
}

// GetParameterList mocks base method.
func (m *MockAmlDevServiceServer) GetParameterList(arg0 context.Context, arg1 *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockAmlDevServiceServerMockRecorder) GetParameterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockAmlDevServiceServer)(nil).GetParameterList), arg0, arg1)
}

// MockUnsafeAmlDevServiceServer is a mock of UnsafeAmlDevServiceServer interface.
type MockUnsafeAmlDevServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAmlDevServiceServerMockRecorder
}

// MockUnsafeAmlDevServiceServerMockRecorder is the mock recorder for MockUnsafeAmlDevServiceServer.
type MockUnsafeAmlDevServiceServerMockRecorder struct {
	mock *MockUnsafeAmlDevServiceServer
}

// NewMockUnsafeAmlDevServiceServer creates a new mock instance.
func NewMockUnsafeAmlDevServiceServer(ctrl *gomock.Controller) *MockUnsafeAmlDevServiceServer {
	mock := &MockUnsafeAmlDevServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAmlDevServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAmlDevServiceServer) EXPECT() *MockUnsafeAmlDevServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAmlDevServiceServer mocks base method.
func (m *MockUnsafeAmlDevServiceServer) mustEmbedUnimplementedAmlDevServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAmlDevServiceServer")
}

// mustEmbedUnimplementedAmlDevServiceServer indicates an expected call of mustEmbedUnimplementedAmlDevServiceServer.
func (mr *MockUnsafeAmlDevServiceServerMockRecorder) mustEmbedUnimplementedAmlDevServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAmlDevServiceServer", reflect.TypeOf((*MockUnsafeAmlDevServiceServer)(nil).mustEmbedUnimplementedAmlDevServiceServer))
}
