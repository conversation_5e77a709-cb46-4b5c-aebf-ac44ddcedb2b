// Code generated by MockGen. DO NOT EDIT.
// Source: api/aml/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	aml "github.com/epifi/gamma/api/aml"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAmlClient is a mock of AmlClient interface.
type MockAmlClient struct {
	ctrl     *gomock.Controller
	recorder *MockAmlClientMockRecorder
}

// MockAmlClientMockRecorder is the mock recorder for MockAmlClient.
type MockAmlClientMockRecorder struct {
	mock *MockAmlClient
}

// NewMockAmlClient creates a new mock instance.
func NewMockAmlClient(ctrl *gomock.Controller) *MockAmlClient {
	mock := &MockAmlClient{ctrl: ctrl}
	mock.recorder = &MockAmlClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAmlClient) EXPECT() *MockAmlClientMockRecorder {
	return m.recorder
}

// ExtractReportData mocks base method.
func (m *MockAmlClient) ExtractReportData(ctx context.Context, in *aml.ExtractReportDataRequest, opts ...grpc.CallOption) (*aml.ExtractReportDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExtractReportData", varargs...)
	ret0, _ := ret[0].(*aml.ExtractReportDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtractReportData indicates an expected call of ExtractReportData.
func (mr *MockAmlClientMockRecorder) ExtractReportData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtractReportData", reflect.TypeOf((*MockAmlClient)(nil).ExtractReportData), varargs...)
}

// GetScreeningStatus mocks base method.
func (m *MockAmlClient) GetScreeningStatus(ctx context.Context, in *aml.GetScreeningStatusRequest, opts ...grpc.CallOption) (*aml.GetScreeningStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScreeningStatus", varargs...)
	ret0, _ := ret[0].(*aml.GetScreeningStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScreeningStatus indicates an expected call of GetScreeningStatus.
func (mr *MockAmlClientMockRecorder) GetScreeningStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScreeningStatus", reflect.TypeOf((*MockAmlClient)(nil).GetScreeningStatus), varargs...)
}

// ScreenCustomer mocks base method.
func (m *MockAmlClient) ScreenCustomer(ctx context.Context, in *aml.ScreenCustomerRequest, opts ...grpc.CallOption) (*aml.ScreenCustomerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ScreenCustomer", varargs...)
	ret0, _ := ret[0].(*aml.ScreenCustomerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScreenCustomer indicates an expected call of ScreenCustomer.
func (mr *MockAmlClientMockRecorder) ScreenCustomer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScreenCustomer", reflect.TypeOf((*MockAmlClient)(nil).ScreenCustomer), varargs...)
}

// SyncCases mocks base method.
func (m *MockAmlClient) SyncCases(ctx context.Context, in *aml.SyncCasesRequest, opts ...grpc.CallOption) (*aml.SyncCasesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SyncCases", varargs...)
	ret0, _ := ret[0].(*aml.SyncCasesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncCases indicates an expected call of SyncCases.
func (mr *MockAmlClientMockRecorder) SyncCases(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncCases", reflect.TypeOf((*MockAmlClient)(nil).SyncCases), varargs...)
}

// UpdateFileGenStatus mocks base method.
func (m *MockAmlClient) UpdateFileGenStatus(ctx context.Context, in *aml.UpdateFileGenStatusRequest, opts ...grpc.CallOption) (*aml.UpdateFileGenStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateFileGenStatus", varargs...)
	ret0, _ := ret[0].(*aml.UpdateFileGenStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateFileGenStatus indicates an expected call of UpdateFileGenStatus.
func (mr *MockAmlClientMockRecorder) UpdateFileGenStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFileGenStatus", reflect.TypeOf((*MockAmlClient)(nil).UpdateFileGenStatus), varargs...)
}

// MockAmlServer is a mock of AmlServer interface.
type MockAmlServer struct {
	ctrl     *gomock.Controller
	recorder *MockAmlServerMockRecorder
}

// MockAmlServerMockRecorder is the mock recorder for MockAmlServer.
type MockAmlServerMockRecorder struct {
	mock *MockAmlServer
}

// NewMockAmlServer creates a new mock instance.
func NewMockAmlServer(ctrl *gomock.Controller) *MockAmlServer {
	mock := &MockAmlServer{ctrl: ctrl}
	mock.recorder = &MockAmlServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAmlServer) EXPECT() *MockAmlServerMockRecorder {
	return m.recorder
}

// ExtractReportData mocks base method.
func (m *MockAmlServer) ExtractReportData(arg0 context.Context, arg1 *aml.ExtractReportDataRequest) (*aml.ExtractReportDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtractReportData", arg0, arg1)
	ret0, _ := ret[0].(*aml.ExtractReportDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtractReportData indicates an expected call of ExtractReportData.
func (mr *MockAmlServerMockRecorder) ExtractReportData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtractReportData", reflect.TypeOf((*MockAmlServer)(nil).ExtractReportData), arg0, arg1)
}

// GetScreeningStatus mocks base method.
func (m *MockAmlServer) GetScreeningStatus(arg0 context.Context, arg1 *aml.GetScreeningStatusRequest) (*aml.GetScreeningStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScreeningStatus", arg0, arg1)
	ret0, _ := ret[0].(*aml.GetScreeningStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScreeningStatus indicates an expected call of GetScreeningStatus.
func (mr *MockAmlServerMockRecorder) GetScreeningStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScreeningStatus", reflect.TypeOf((*MockAmlServer)(nil).GetScreeningStatus), arg0, arg1)
}

// ScreenCustomer mocks base method.
func (m *MockAmlServer) ScreenCustomer(arg0 context.Context, arg1 *aml.ScreenCustomerRequest) (*aml.ScreenCustomerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScreenCustomer", arg0, arg1)
	ret0, _ := ret[0].(*aml.ScreenCustomerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScreenCustomer indicates an expected call of ScreenCustomer.
func (mr *MockAmlServerMockRecorder) ScreenCustomer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScreenCustomer", reflect.TypeOf((*MockAmlServer)(nil).ScreenCustomer), arg0, arg1)
}

// SyncCases mocks base method.
func (m *MockAmlServer) SyncCases(arg0 context.Context, arg1 *aml.SyncCasesRequest) (*aml.SyncCasesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncCases", arg0, arg1)
	ret0, _ := ret[0].(*aml.SyncCasesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncCases indicates an expected call of SyncCases.
func (mr *MockAmlServerMockRecorder) SyncCases(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncCases", reflect.TypeOf((*MockAmlServer)(nil).SyncCases), arg0, arg1)
}

// UpdateFileGenStatus mocks base method.
func (m *MockAmlServer) UpdateFileGenStatus(arg0 context.Context, arg1 *aml.UpdateFileGenStatusRequest) (*aml.UpdateFileGenStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFileGenStatus", arg0, arg1)
	ret0, _ := ret[0].(*aml.UpdateFileGenStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateFileGenStatus indicates an expected call of UpdateFileGenStatus.
func (mr *MockAmlServerMockRecorder) UpdateFileGenStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFileGenStatus", reflect.TypeOf((*MockAmlServer)(nil).UpdateFileGenStatus), arg0, arg1)
}

// MockUnsafeAmlServer is a mock of UnsafeAmlServer interface.
type MockUnsafeAmlServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAmlServerMockRecorder
}

// MockUnsafeAmlServerMockRecorder is the mock recorder for MockUnsafeAmlServer.
type MockUnsafeAmlServerMockRecorder struct {
	mock *MockUnsafeAmlServer
}

// NewMockUnsafeAmlServer creates a new mock instance.
func NewMockUnsafeAmlServer(ctrl *gomock.Controller) *MockUnsafeAmlServer {
	mock := &MockUnsafeAmlServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAmlServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAmlServer) EXPECT() *MockUnsafeAmlServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAmlServer mocks base method.
func (m *MockUnsafeAmlServer) mustEmbedUnimplementedAmlServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAmlServer")
}

// mustEmbedUnimplementedAmlServer indicates an expected call of mustEmbedUnimplementedAmlServer.
func (mr *MockUnsafeAmlServerMockRecorder) mustEmbedUnimplementedAmlServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAmlServer", reflect.TypeOf((*MockUnsafeAmlServer)(nil).mustEmbedUnimplementedAmlServer))
}
