// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/aml/service.proto

package aml

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	aml "github.com/epifi/gamma/api/vendorgateway/aml"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = aml.CaseCategory(0)

	_ = common.Owner(0)
)

// Validate checks the field values on UpdateFileGenStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFileGenStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFileGenStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFileGenStatusRequestMultiError, or nil if none found.
func (m *UpdateFileGenStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFileGenStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Status

	if len(errors) > 0 {
		return UpdateFileGenStatusRequestMultiError(errors)
	}

	return nil
}

// UpdateFileGenStatusRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateFileGenStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateFileGenStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFileGenStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFileGenStatusRequestMultiError) AllErrors() []error { return m }

// UpdateFileGenStatusRequestValidationError is the validation error returned
// by UpdateFileGenStatusRequest.Validate if the designated constraints aren't met.
type UpdateFileGenStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFileGenStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFileGenStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFileGenStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFileGenStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFileGenStatusRequestValidationError) ErrorName() string {
	return "UpdateFileGenStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFileGenStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFileGenStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFileGenStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFileGenStatusRequestValidationError{}

// Validate checks the field values on UpdateFileGenStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFileGenStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFileGenStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFileGenStatusResponseMultiError, or nil if none found.
func (m *UpdateFileGenStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFileGenStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFileGenStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFileGenStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFileGenStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateFileGenStatusResponseMultiError(errors)
	}

	return nil
}

// UpdateFileGenStatusResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateFileGenStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateFileGenStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFileGenStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFileGenStatusResponseMultiError) AllErrors() []error { return m }

// UpdateFileGenStatusResponseValidationError is the validation error returned
// by UpdateFileGenStatusResponse.Validate if the designated constraints
// aren't met.
type UpdateFileGenStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFileGenStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFileGenStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFileGenStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFileGenStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFileGenStatusResponseValidationError) ErrorName() string {
	return "UpdateFileGenStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFileGenStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFileGenStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFileGenStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFileGenStatusResponseValidationError{}

// Validate checks the field values on ExtractReportDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExtractReportDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExtractReportDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExtractReportDataRequestMultiError, or nil if none found.
func (m *ExtractReportDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExtractReportDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReportType

	// no validation rules for FileData

	if len(errors) > 0 {
		return ExtractReportDataRequestMultiError(errors)
	}

	return nil
}

// ExtractReportDataRequestMultiError is an error wrapping multiple validation
// errors returned by ExtractReportDataRequest.ValidateAll() if the designated
// constraints aren't met.
type ExtractReportDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExtractReportDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExtractReportDataRequestMultiError) AllErrors() []error { return m }

// ExtractReportDataRequestValidationError is the validation error returned by
// ExtractReportDataRequest.Validate if the designated constraints aren't met.
type ExtractReportDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExtractReportDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExtractReportDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExtractReportDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExtractReportDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExtractReportDataRequestValidationError) ErrorName() string {
	return "ExtractReportDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ExtractReportDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExtractReportDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExtractReportDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExtractReportDataRequestValidationError{}

// Validate checks the field values on ExtractReportDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExtractReportDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExtractReportDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExtractReportDataResponseMultiError, or nil if none found.
func (m *ExtractReportDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ExtractReportDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExtractReportDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExtractReportDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExtractReportDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExtractReportDataResponseMultiError(errors)
	}

	return nil
}

// ExtractReportDataResponseMultiError is an error wrapping multiple validation
// errors returned by ExtractReportDataResponse.ValidateAll() if the
// designated constraints aren't met.
type ExtractReportDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExtractReportDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExtractReportDataResponseMultiError) AllErrors() []error { return m }

// ExtractReportDataResponseValidationError is the validation error returned by
// ExtractReportDataResponse.Validate if the designated constraints aren't met.
type ExtractReportDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExtractReportDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExtractReportDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExtractReportDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExtractReportDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExtractReportDataResponseValidationError) ErrorName() string {
	return "ExtractReportDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ExtractReportDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExtractReportDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExtractReportDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExtractReportDataResponseValidationError{}

// Validate checks the field values on ScreenCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScreenCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScreenCustomerRequestMultiError, or nil if none found.
func (m *ScreenCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := ScreenCustomerRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AmlEntity

	if _, ok := _ScreenCustomerRequest_AmlProduct_NotInLookup[m.GetAmlProduct()]; ok {
		err := ScreenCustomerRequestValidationError{
			field:  "AmlProduct",
			reason: "value must not be in list [AML_PRODUCT_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCustomerDetails() == nil {
		err := ScreenCustomerRequestValidationError{
			field:  "CustomerDetails",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCustomerDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScreenCustomerRequestValidationError{
					field:  "CustomerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScreenCustomerRequestValidationError{
					field:  "CustomerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomerDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScreenCustomerRequestValidationError{
				field:  "CustomerDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _ScreenCustomerRequest_AmlScreeningMode_NotInLookup[m.GetAmlScreeningMode()]; ok {
		err := ScreenCustomerRequestValidationError{
			field:  "AmlScreeningMode",
			reason: "value must not be in list [AML_SCREENING_MODE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetClientRequestId()) < 1 {
		err := ScreenCustomerRequestValidationError{
			field:  "ClientRequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Owner

	if len(errors) > 0 {
		return ScreenCustomerRequestMultiError(errors)
	}

	return nil
}

// ScreenCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by ScreenCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type ScreenCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenCustomerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenCustomerRequestMultiError) AllErrors() []error { return m }

// ScreenCustomerRequestValidationError is the validation error returned by
// ScreenCustomerRequest.Validate if the designated constraints aren't met.
type ScreenCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenCustomerRequestValidationError) ErrorName() string {
	return "ScreenCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ScreenCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenCustomerRequestValidationError{}

var _ScreenCustomerRequest_AmlProduct_NotInLookup = map[AmlProduct]struct{}{
	0: {},
}

var _ScreenCustomerRequest_AmlScreeningMode_NotInLookup = map[AmlScreeningMode]struct{}{
	0: {},
}

// Validate checks the field values on ScreenCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScreenCustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScreenCustomerResponseMultiError, or nil if none found.
func (m *ScreenCustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenCustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScreenCustomerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScreenCustomerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScreenCustomerResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Match

	for idx, item := range m.GetMatchData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ScreenCustomerResponseValidationError{
						field:  fmt.Sprintf("MatchData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ScreenCustomerResponseValidationError{
						field:  fmt.Sprintf("MatchData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ScreenCustomerResponseValidationError{
					field:  fmt.Sprintf("MatchData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ScreenCustomerResponseMultiError(errors)
	}

	return nil
}

// ScreenCustomerResponseMultiError is an error wrapping multiple validation
// errors returned by ScreenCustomerResponse.ValidateAll() if the designated
// constraints aren't met.
type ScreenCustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenCustomerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenCustomerResponseMultiError) AllErrors() []error { return m }

// ScreenCustomerResponseValidationError is the validation error returned by
// ScreenCustomerResponse.Validate if the designated constraints aren't met.
type ScreenCustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenCustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenCustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenCustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenCustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenCustomerResponseValidationError) ErrorName() string {
	return "ScreenCustomerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ScreenCustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenCustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenCustomerResponseValidationError{}

// Validate checks the field values on GetScreeningStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetScreeningStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScreeningStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetScreeningStatusRequestMultiError, or nil if none found.
func (m *GetScreeningStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScreeningStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetClientRequestId()) < 1 {
		err := GetScreeningStatusRequestValidationError{
			field:  "ClientRequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Owner

	if len(errors) > 0 {
		return GetScreeningStatusRequestMultiError(errors)
	}

	return nil
}

// GetScreeningStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetScreeningStatusRequest.ValidateAll() if the
// designated constraints aren't met.
type GetScreeningStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScreeningStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScreeningStatusRequestMultiError) AllErrors() []error { return m }

// GetScreeningStatusRequestValidationError is the validation error returned by
// GetScreeningStatusRequest.Validate if the designated constraints aren't met.
type GetScreeningStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScreeningStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScreeningStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScreeningStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScreeningStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScreeningStatusRequestValidationError) ErrorName() string {
	return "GetScreeningStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetScreeningStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScreeningStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScreeningStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScreeningStatusRequestValidationError{}

// Validate checks the field values on GetScreeningStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetScreeningStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScreeningStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetScreeningStatusResponseMultiError, or nil if none found.
func (m *GetScreeningStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScreeningStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScreeningStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScreeningStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScreeningStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ScreeningStatus

	// no validation rules for MatchFound

	for idx, item := range m.GetMatchData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetScreeningStatusResponseValidationError{
						field:  fmt.Sprintf("MatchData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetScreeningStatusResponseValidationError{
						field:  fmt.Sprintf("MatchData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetScreeningStatusResponseValidationError{
					field:  fmt.Sprintf("MatchData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Match

	// no validation rules for ReviewStatus

	if len(errors) > 0 {
		return GetScreeningStatusResponseMultiError(errors)
	}

	return nil
}

// GetScreeningStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetScreeningStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type GetScreeningStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScreeningStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScreeningStatusResponseMultiError) AllErrors() []error { return m }

// GetScreeningStatusResponseValidationError is the validation error returned
// by GetScreeningStatusResponse.Validate if the designated constraints aren't met.
type GetScreeningStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScreeningStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScreeningStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScreeningStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScreeningStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScreeningStatusResponseValidationError) ErrorName() string {
	return "GetScreeningStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetScreeningStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScreeningStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScreeningStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScreeningStatusResponseValidationError{}

// Validate checks the field values on SyncCasesRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SyncCasesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncCasesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncCasesRequestMultiError, or nil if none found.
func (m *SyncCasesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncCasesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := common.Owner_name[int32(m.GetOwner())]; !ok {
		err := SyncCasesRequestValidationError{
			field:  "Owner",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFromDateTime() == nil {
		err := SyncCasesRequestValidationError{
			field:  "FromDateTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetToDateTime() == nil {
		err := SyncCasesRequestValidationError{
			field:  "ToDateTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SyncCasesRequestMultiError(errors)
	}

	return nil
}

// SyncCasesRequestMultiError is an error wrapping multiple validation errors
// returned by SyncCasesRequest.ValidateAll() if the designated constraints
// aren't met.
type SyncCasesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncCasesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncCasesRequestMultiError) AllErrors() []error { return m }

// SyncCasesRequestValidationError is the validation error returned by
// SyncCasesRequest.Validate if the designated constraints aren't met.
type SyncCasesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncCasesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncCasesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncCasesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncCasesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncCasesRequestValidationError) ErrorName() string { return "SyncCasesRequestValidationError" }

// Error satisfies the builtin error interface
func (e SyncCasesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncCasesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncCasesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncCasesRequestValidationError{}

// Validate checks the field values on SyncCasesResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SyncCasesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncCasesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncCasesResponseMultiError, or nil if none found.
func (m *SyncCasesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncCasesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SyncCasesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SyncCasesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SyncCasesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SyncCasesResponseMultiError(errors)
	}

	return nil
}

// SyncCasesResponseMultiError is an error wrapping multiple validation errors
// returned by SyncCasesResponse.ValidateAll() if the designated constraints
// aren't met.
type SyncCasesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncCasesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncCasesResponseMultiError) AllErrors() []error { return m }

// SyncCasesResponseValidationError is the validation error returned by
// SyncCasesResponse.Validate if the designated constraints aren't met.
type SyncCasesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncCasesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncCasesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncCasesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncCasesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncCasesResponseValidationError) ErrorName() string {
	return "SyncCasesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SyncCasesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncCasesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncCasesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncCasesResponseValidationError{}
