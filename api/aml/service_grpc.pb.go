// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/aml/service.proto

package aml

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Aml_ScreenCustomer_FullMethodName      = "/aml.Aml/ScreenCustomer"
	Aml_GetScreeningStatus_FullMethodName  = "/aml.Aml/GetScreeningStatus"
	Aml_ExtractReportData_FullMethodName   = "/aml.Aml/ExtractReportData"
	Aml_UpdateFileGenStatus_FullMethodName = "/aml.Aml/UpdateFileGenStatus"
	Aml_SyncCases_FullMethodName           = "/aml.Aml/SyncCases"
)

// AmlClient is the client API for Aml service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AmlClient interface {
	// rpc to initiate AML screening or update existing user's data in AML system
	ScreenCustomer(ctx context.Context, in *ScreenCustomerRequest, opts ...grpc.CallOption) (*ScreenCustomerResponse, error)
	// rpc to get the screening status and match details if matched
	GetScreeningStatus(ctx context.Context, in *GetScreeningStatusRequest, opts ...grpc.CallOption) (*GetScreeningStatusResponse, error)
	// rpc to extract aml report data uploaded by agent on sherlock
	ExtractReportData(ctx context.Context, in *ExtractReportDataRequest, opts ...grpc.CallOption) (*ExtractReportDataResponse, error)
	// rpc to update file generation status for a particular actor. This will be useful when
	// file generation fails for an actor, status can be updated to FILE_GENERATION_STATUS_RETRY_NEEDED to retry next time
	UpdateFileGenStatus(ctx context.Context, in *UpdateFileGenStatusRequest, opts ...grpc.CallOption) (*UpdateFileGenStatusResponse, error)
	// SyncCases creates or updates cases from AML vendor to our case details table
	// This RPC may be called at periodic intervals to keep the case details table up to date.
	SyncCases(ctx context.Context, in *SyncCasesRequest, opts ...grpc.CallOption) (*SyncCasesResponse, error)
}

type amlClient struct {
	cc grpc.ClientConnInterface
}

func NewAmlClient(cc grpc.ClientConnInterface) AmlClient {
	return &amlClient{cc}
}

func (c *amlClient) ScreenCustomer(ctx context.Context, in *ScreenCustomerRequest, opts ...grpc.CallOption) (*ScreenCustomerResponse, error) {
	out := new(ScreenCustomerResponse)
	err := c.cc.Invoke(ctx, Aml_ScreenCustomer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *amlClient) GetScreeningStatus(ctx context.Context, in *GetScreeningStatusRequest, opts ...grpc.CallOption) (*GetScreeningStatusResponse, error) {
	out := new(GetScreeningStatusResponse)
	err := c.cc.Invoke(ctx, Aml_GetScreeningStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *amlClient) ExtractReportData(ctx context.Context, in *ExtractReportDataRequest, opts ...grpc.CallOption) (*ExtractReportDataResponse, error) {
	out := new(ExtractReportDataResponse)
	err := c.cc.Invoke(ctx, Aml_ExtractReportData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *amlClient) UpdateFileGenStatus(ctx context.Context, in *UpdateFileGenStatusRequest, opts ...grpc.CallOption) (*UpdateFileGenStatusResponse, error) {
	out := new(UpdateFileGenStatusResponse)
	err := c.cc.Invoke(ctx, Aml_UpdateFileGenStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *amlClient) SyncCases(ctx context.Context, in *SyncCasesRequest, opts ...grpc.CallOption) (*SyncCasesResponse, error) {
	out := new(SyncCasesResponse)
	err := c.cc.Invoke(ctx, Aml_SyncCases_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AmlServer is the server API for Aml service.
// All implementations should embed UnimplementedAmlServer
// for forward compatibility
type AmlServer interface {
	// rpc to initiate AML screening or update existing user's data in AML system
	ScreenCustomer(context.Context, *ScreenCustomerRequest) (*ScreenCustomerResponse, error)
	// rpc to get the screening status and match details if matched
	GetScreeningStatus(context.Context, *GetScreeningStatusRequest) (*GetScreeningStatusResponse, error)
	// rpc to extract aml report data uploaded by agent on sherlock
	ExtractReportData(context.Context, *ExtractReportDataRequest) (*ExtractReportDataResponse, error)
	// rpc to update file generation status for a particular actor. This will be useful when
	// file generation fails for an actor, status can be updated to FILE_GENERATION_STATUS_RETRY_NEEDED to retry next time
	UpdateFileGenStatus(context.Context, *UpdateFileGenStatusRequest) (*UpdateFileGenStatusResponse, error)
	// SyncCases creates or updates cases from AML vendor to our case details table
	// This RPC may be called at periodic intervals to keep the case details table up to date.
	SyncCases(context.Context, *SyncCasesRequest) (*SyncCasesResponse, error)
}

// UnimplementedAmlServer should be embedded to have forward compatible implementations.
type UnimplementedAmlServer struct {
}

func (UnimplementedAmlServer) ScreenCustomer(context.Context, *ScreenCustomerRequest) (*ScreenCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ScreenCustomer not implemented")
}
func (UnimplementedAmlServer) GetScreeningStatus(context.Context, *GetScreeningStatusRequest) (*GetScreeningStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetScreeningStatus not implemented")
}
func (UnimplementedAmlServer) ExtractReportData(context.Context, *ExtractReportDataRequest) (*ExtractReportDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtractReportData not implemented")
}
func (UnimplementedAmlServer) UpdateFileGenStatus(context.Context, *UpdateFileGenStatusRequest) (*UpdateFileGenStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFileGenStatus not implemented")
}
func (UnimplementedAmlServer) SyncCases(context.Context, *SyncCasesRequest) (*SyncCasesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncCases not implemented")
}

// UnsafeAmlServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AmlServer will
// result in compilation errors.
type UnsafeAmlServer interface {
	mustEmbedUnimplementedAmlServer()
}

func RegisterAmlServer(s grpc.ServiceRegistrar, srv AmlServer) {
	s.RegisterService(&Aml_ServiceDesc, srv)
}

func _Aml_ScreenCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScreenCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AmlServer).ScreenCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aml_ScreenCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AmlServer).ScreenCustomer(ctx, req.(*ScreenCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aml_GetScreeningStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScreeningStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AmlServer).GetScreeningStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aml_GetScreeningStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AmlServer).GetScreeningStatus(ctx, req.(*GetScreeningStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aml_ExtractReportData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtractReportDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AmlServer).ExtractReportData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aml_ExtractReportData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AmlServer).ExtractReportData(ctx, req.(*ExtractReportDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aml_UpdateFileGenStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFileGenStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AmlServer).UpdateFileGenStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aml_UpdateFileGenStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AmlServer).UpdateFileGenStatus(ctx, req.(*UpdateFileGenStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aml_SyncCases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncCasesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AmlServer).SyncCases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aml_SyncCases_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AmlServer).SyncCases(ctx, req.(*SyncCasesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Aml_ServiceDesc is the grpc.ServiceDesc for Aml service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Aml_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "aml.Aml",
	HandlerType: (*AmlServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ScreenCustomer",
			Handler:    _Aml_ScreenCustomer_Handler,
		},
		{
			MethodName: "GetScreeningStatus",
			Handler:    _Aml_GetScreeningStatus_Handler,
		},
		{
			MethodName: "ExtractReportData",
			Handler:    _Aml_ExtractReportData_Handler,
		},
		{
			MethodName: "UpdateFileGenStatus",
			Handler:    _Aml_UpdateFileGenStatus_Handler,
		},
		{
			MethodName: "SyncCases",
			Handler:    _Aml_SyncCases_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/aml/service.proto",
}
