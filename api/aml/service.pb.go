// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/aml/service.proto

package aml

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	aml "github.com/epifi/gamma/api/vendorgateway/aml"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AmlReportType int32

const (
	AmlReportType_AML_REPORT_TYPE_UNSPECIFIED AmlReportType = 0
	// Report extracted from TSS vendor's portal containing the details regarding all the alerts
	AmlReportType_AML_REPORT_TYPE_TSS_ALERT_DETAILS_R508 AmlReportType = 1
)

// Enum value maps for AmlReportType.
var (
	AmlReportType_name = map[int32]string{
		0: "AML_REPORT_TYPE_UNSPECIFIED",
		1: "AML_REPORT_TYPE_TSS_ALERT_DETAILS_R508",
	}
	AmlReportType_value = map[string]int32{
		"AML_REPORT_TYPE_UNSPECIFIED":            0,
		"AML_REPORT_TYPE_TSS_ALERT_DETAILS_R508": 1,
	}
)

func (x AmlReportType) Enum() *AmlReportType {
	p := new(AmlReportType)
	*p = x
	return p
}

func (x AmlReportType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AmlReportType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_aml_service_proto_enumTypes[0].Descriptor()
}

func (AmlReportType) Type() protoreflect.EnumType {
	return &file_api_aml_service_proto_enumTypes[0]
}

func (x AmlReportType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AmlReportType.Descriptor instead.
func (AmlReportType) EnumDescriptor() ([]byte, []int) {
	return file_api_aml_service_proto_rawDescGZIP(), []int{0}
}

type ScreenCustomerResponse_Status int32

const (
	ScreenCustomerResponse_OK ScreenCustomerResponse_Status = 0
	// Deprecated: Marked as deprecated in api/aml/service.proto.
	ScreenCustomerResponse_FIRST_NAME_CONTAINS_INVALID_CHARACTERS ScreenCustomerResponse_Status = 101
	// Deprecated: Marked as deprecated in api/aml/service.proto.
	ScreenCustomerResponse_FIRST_NAME_MISSING ScreenCustomerResponse_Status = 102
	// Deprecated: Marked as deprecated in api/aml/service.proto.
	ScreenCustomerResponse_LAST_NAME_CONTAINS_INVALID_CHARACTERS ScreenCustomerResponse_Status = 103
	// Deprecated: Marked as deprecated in api/aml/service.proto.
	ScreenCustomerResponse_MIDDLE_NAME_CONTAINS_INVALID_CHARACTERS ScreenCustomerResponse_Status = 104
	ScreenCustomerResponse_INVALID_PAN_FORMAT                      ScreenCustomerResponse_Status = 105
	ScreenCustomerResponse_PASSPORT_LENGTH_EXCEEDED                ScreenCustomerResponse_Status = 106
)

// Enum value maps for ScreenCustomerResponse_Status.
var (
	ScreenCustomerResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "FIRST_NAME_CONTAINS_INVALID_CHARACTERS",
		102: "FIRST_NAME_MISSING",
		103: "LAST_NAME_CONTAINS_INVALID_CHARACTERS",
		104: "MIDDLE_NAME_CONTAINS_INVALID_CHARACTERS",
		105: "INVALID_PAN_FORMAT",
		106: "PASSPORT_LENGTH_EXCEEDED",
	}
	ScreenCustomerResponse_Status_value = map[string]int32{
		"OK":                                      0,
		"FIRST_NAME_CONTAINS_INVALID_CHARACTERS":  101,
		"FIRST_NAME_MISSING":                      102,
		"LAST_NAME_CONTAINS_INVALID_CHARACTERS":   103,
		"MIDDLE_NAME_CONTAINS_INVALID_CHARACTERS": 104,
		"INVALID_PAN_FORMAT":                      105,
		"PASSPORT_LENGTH_EXCEEDED":                106,
	}
)

func (x ScreenCustomerResponse_Status) Enum() *ScreenCustomerResponse_Status {
	p := new(ScreenCustomerResponse_Status)
	*p = x
	return p
}

func (x ScreenCustomerResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScreenCustomerResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_aml_service_proto_enumTypes[1].Descriptor()
}

func (ScreenCustomerResponse_Status) Type() protoreflect.EnumType {
	return &file_api_aml_service_proto_enumTypes[1]
}

func (x ScreenCustomerResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScreenCustomerResponse_Status.Descriptor instead.
func (ScreenCustomerResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_aml_service_proto_rawDescGZIP(), []int{5, 0}
}

type UpdateFileGenStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary id of client id to actor id mapping
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// new file generation status
	Status FileGenerationStatus `protobuf:"varint,2,opt,name=status,proto3,enum=aml.FileGenerationStatus" json:"status,omitempty"`
}

func (x *UpdateFileGenStatusRequest) Reset() {
	*x = UpdateFileGenStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_aml_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateFileGenStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFileGenStatusRequest) ProtoMessage() {}

func (x *UpdateFileGenStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_aml_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFileGenStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateFileGenStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_aml_service_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateFileGenStatusRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateFileGenStatusRequest) GetStatus() FileGenerationStatus {
	if x != nil {
		return x.Status
	}
	return FileGenerationStatus_FILE_GENERATION_STATUS_UNSPECIFIED
}

type UpdateFileGenStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateFileGenStatusResponse) Reset() {
	*x = UpdateFileGenStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_aml_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateFileGenStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFileGenStatusResponse) ProtoMessage() {}

func (x *UpdateFileGenStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_aml_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFileGenStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateFileGenStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_aml_service_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateFileGenStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ExtractReportDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type of the report
	ReportType AmlReportType `protobuf:"varint,1,opt,name=report_type,json=reportType,proto3,enum=aml.AmlReportType" json:"report_type,omitempty"`
	// binary data of the report
	FileData []byte `protobuf:"bytes,2,opt,name=file_data,json=fileData,proto3" json:"file_data,omitempty"`
}

func (x *ExtractReportDataRequest) Reset() {
	*x = ExtractReportDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_aml_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtractReportDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractReportDataRequest) ProtoMessage() {}

func (x *ExtractReportDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_aml_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractReportDataRequest.ProtoReflect.Descriptor instead.
func (*ExtractReportDataRequest) Descriptor() ([]byte, []int) {
	return file_api_aml_service_proto_rawDescGZIP(), []int{2}
}

func (x *ExtractReportDataRequest) GetReportType() AmlReportType {
	if x != nil {
		return x.ReportType
	}
	return AmlReportType_AML_REPORT_TYPE_UNSPECIFIED
}

func (x *ExtractReportDataRequest) GetFileData() []byte {
	if x != nil {
		return x.FileData
	}
	return nil
}

type ExtractReportDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ExtractReportDataResponse) Reset() {
	*x = ExtractReportDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_aml_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtractReportDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractReportDataResponse) ProtoMessage() {}

func (x *ExtractReportDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_aml_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractReportDataResponse.ProtoReflect.Descriptor instead.
func (*ExtractReportDataResponse) Descriptor() ([]byte, []int) {
	return file_api_aml_service_proto_rawDescGZIP(), []int{3}
}

func (x *ExtractReportDataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ScreenCustomerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// deprecated since AML screening rules are configured based on AmlProduct and this is of no use
	//
	// Deprecated: Marked as deprecated in api/aml/service.proto.
	AmlEntity       AmlEntity        `protobuf:"varint,2,opt,name=aml_entity,json=amlEntity,proto3,enum=aml.AmlEntity" json:"aml_entity,omitempty"`
	AmlProduct      AmlProduct       `protobuf:"varint,3,opt,name=aml_product,json=amlProduct,proto3,enum=aml.AmlProduct" json:"aml_product,omitempty"`
	CustomerDetails *CustomerDetails `protobuf:"bytes,4,opt,name=customer_details,json=customerDetails,proto3" json:"customer_details,omitempty"`
	// In case of update mode, only the fields which need to be updated can be passed
	AmlScreeningMode AmlScreeningMode `protobuf:"varint,5,opt,name=aml_screening_mode,json=amlScreeningMode,proto3,enum=aml.AmlScreeningMode" json:"aml_screening_mode,omitempty"`
	ClientRequestId  string           `protobuf:"bytes,6,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// Owner of the screening request.
	// Required for adhering to data segregation compliance requirements of each regulatory body.
	Owner common.Owner `protobuf:"varint,7,opt,name=owner,proto3,enum=api.typesv2.common.Owner" json:"owner,omitempty"`
}

func (x *ScreenCustomerRequest) Reset() {
	*x = ScreenCustomerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_aml_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenCustomerRequest) ProtoMessage() {}

func (x *ScreenCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_aml_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenCustomerRequest.ProtoReflect.Descriptor instead.
func (*ScreenCustomerRequest) Descriptor() ([]byte, []int) {
	return file_api_aml_service_proto_rawDescGZIP(), []int{4}
}

func (x *ScreenCustomerRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/aml/service.proto.
func (x *ScreenCustomerRequest) GetAmlEntity() AmlEntity {
	if x != nil {
		return x.AmlEntity
	}
	return AmlEntity_AML_ENTITY_UNSPECIFIED
}

func (x *ScreenCustomerRequest) GetAmlProduct() AmlProduct {
	if x != nil {
		return x.AmlProduct
	}
	return AmlProduct_AML_PRODUCT_UNSPECIFIED
}

func (x *ScreenCustomerRequest) GetCustomerDetails() *CustomerDetails {
	if x != nil {
		return x.CustomerDetails
	}
	return nil
}

func (x *ScreenCustomerRequest) GetAmlScreeningMode() AmlScreeningMode {
	if x != nil {
		return x.AmlScreeningMode
	}
	return AmlScreeningMode_AML_SCREENING_MODE_UNSPECIFIED
}

func (x *ScreenCustomerRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *ScreenCustomerRequest) GetOwner() common.Owner {
	if x != nil {
		return x.Owner
	}
	return common.Owner(0)
}

type ScreenCustomerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// match found in the screening attempt
	Match AmlMatch `protobuf:"varint,2,opt,name=match,proto3,enum=aml.AmlMatch" json:"match,omitempty"`
	// in case match is found, relevant data is populated here
	MatchData []*MatchData `protobuf:"bytes,3,rep,name=match_data,json=matchData,proto3" json:"match_data,omitempty"`
}

func (x *ScreenCustomerResponse) Reset() {
	*x = ScreenCustomerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_aml_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenCustomerResponse) ProtoMessage() {}

func (x *ScreenCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_aml_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenCustomerResponse.ProtoReflect.Descriptor instead.
func (*ScreenCustomerResponse) Descriptor() ([]byte, []int) {
	return file_api_aml_service_proto_rawDescGZIP(), []int{5}
}

func (x *ScreenCustomerResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ScreenCustomerResponse) GetMatch() AmlMatch {
	if x != nil {
		return x.Match
	}
	return AmlMatch_AML_MATCH_UNSPECIFIED
}

func (x *ScreenCustomerResponse) GetMatchData() []*MatchData {
	if x != nil {
		return x.MatchData
	}
	return nil
}

type GetScreeningStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientRequestId string `protobuf:"bytes,1,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// Owner of the screening request.
	// Required for adhering to data segregation compliance requirements of each regulatory body.
	Owner common.Owner `protobuf:"varint,2,opt,name=owner,proto3,enum=api.typesv2.common.Owner" json:"owner,omitempty"`
}

func (x *GetScreeningStatusRequest) Reset() {
	*x = GetScreeningStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_aml_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScreeningStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScreeningStatusRequest) ProtoMessage() {}

func (x *GetScreeningStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_aml_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScreeningStatusRequest.ProtoReflect.Descriptor instead.
func (*GetScreeningStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_aml_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetScreeningStatusRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *GetScreeningStatusRequest) GetOwner() common.Owner {
	if x != nil {
		return x.Owner
	}
	return common.Owner(0)
}

type GetScreeningStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// status of the screening request
	ScreeningStatus AmlScreeningStatus `protobuf:"varint,2,opt,name=screening_status,json=screeningStatus,proto3,enum=aml.AmlScreeningStatus" json:"screening_status,omitempty"`
	// match found in the screening attempt
	//
	// Deprecated: Marked as deprecated in api/aml/service.proto.
	MatchFound bool `protobuf:"varint,3,opt,name=match_found,json=matchFound,proto3" json:"match_found,omitempty"`
	// in case match is found, relevant data is populated here
	MatchData []*MatchData `protobuf:"bytes,4,rep,name=match_data,json=matchData,proto3" json:"match_data,omitempty"`
	// match found in the screening attempt
	Match AmlMatch `protobuf:"varint,5,opt,name=match,proto3,enum=aml.AmlMatch" json:"match,omitempty"`
	// Status of the review of the case opened against a screening attempt when a match is found
	ReviewStatus ReviewStatus `protobuf:"varint,6,opt,name=review_status,json=reviewStatus,proto3,enum=aml.ReviewStatus" json:"review_status,omitempty"`
}

func (x *GetScreeningStatusResponse) Reset() {
	*x = GetScreeningStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_aml_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScreeningStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScreeningStatusResponse) ProtoMessage() {}

func (x *GetScreeningStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_aml_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScreeningStatusResponse.ProtoReflect.Descriptor instead.
func (*GetScreeningStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_aml_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetScreeningStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetScreeningStatusResponse) GetScreeningStatus() AmlScreeningStatus {
	if x != nil {
		return x.ScreeningStatus
	}
	return AmlScreeningStatus_AML_SCREENING_STATUS_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/aml/service.proto.
func (x *GetScreeningStatusResponse) GetMatchFound() bool {
	if x != nil {
		return x.MatchFound
	}
	return false
}

func (x *GetScreeningStatusResponse) GetMatchData() []*MatchData {
	if x != nil {
		return x.MatchData
	}
	return nil
}

func (x *GetScreeningStatusResponse) GetMatch() AmlMatch {
	if x != nil {
		return x.Match
	}
	return AmlMatch_AML_MATCH_UNSPECIFIED
}

func (x *GetScreeningStatusResponse) GetReviewStatus() ReviewStatus {
	if x != nil {
		return x.ReviewStatus
	}
	return ReviewStatus_REVIEW_STATUS_UNSPECIFIED
}

type SyncCasesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// owner for which to sync cases
	Owner common.Owner `protobuf:"varint,1,opt,name=owner,proto3,enum=api.typesv2.common.Owner" json:"owner,omitempty"`
	// case categories to filter by. If not provided, all categories except unspecified will be used
	CaseCategories []aml.CaseCategory `protobuf:"varint,2,rep,packed,name=case_categories,json=caseCategories,proto3,enum=vendorgateway.aml.CaseCategory" json:"case_categories,omitempty"`
	// case types to filter by. If not provided, all types except unspecified will be used
	CaseTypes []aml.CaseType `protobuf:"varint,3,rep,packed,name=case_types,json=caseTypes,proto3,enum=vendorgateway.aml.CaseType" json:"case_types,omitempty"`
	// start date for the sync range
	FromDateTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=from_date_time,json=fromDateTime,proto3" json:"from_date_time,omitempty"`
	// end date for the sync range
	ToDateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=to_date_time,json=toDateTime,proto3" json:"to_date_time,omitempty"`
}

func (x *SyncCasesRequest) Reset() {
	*x = SyncCasesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_aml_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncCasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncCasesRequest) ProtoMessage() {}

func (x *SyncCasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_aml_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncCasesRequest.ProtoReflect.Descriptor instead.
func (*SyncCasesRequest) Descriptor() ([]byte, []int) {
	return file_api_aml_service_proto_rawDescGZIP(), []int{8}
}

func (x *SyncCasesRequest) GetOwner() common.Owner {
	if x != nil {
		return x.Owner
	}
	return common.Owner(0)
}

func (x *SyncCasesRequest) GetCaseCategories() []aml.CaseCategory {
	if x != nil {
		return x.CaseCategories
	}
	return nil
}

func (x *SyncCasesRequest) GetCaseTypes() []aml.CaseType {
	if x != nil {
		return x.CaseTypes
	}
	return nil
}

func (x *SyncCasesRequest) GetFromDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromDateTime
	}
	return nil
}

func (x *SyncCasesRequest) GetToDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ToDateTime
	}
	return nil
}

type SyncCasesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SyncCasesResponse) Reset() {
	*x = SyncCasesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_aml_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncCasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncCasesResponse) ProtoMessage() {}

func (x *SyncCasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_aml_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncCasesResponse.ProtoReflect.Descriptor instead.
func (*SyncCasesResponse) Descriptor() ([]byte, []int) {
	return file_api_aml_service_proto_rawDescGZIP(), []int{9}
}

func (x *SyncCasesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_aml_service_proto protoreflect.FileDescriptor

var file_api_aml_service_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6d, 0x6c, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x61, 0x6d, 0x6c, 0x1a, 0x1e, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6d, 0x6c, 0x2f, 0x61, 0x6d, 0x6c, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6d, 0x6c, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x61, 0x6d,
	0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5f, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x47, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x46, 0x69, 0x6c,
	0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x42, 0x0a, 0x1b, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x47, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x6c, 0x0a,
	0x18, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x0b, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12,
	0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x41, 0x6d, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x40, 0x0a, 0x19, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xaa, 0x03,
	0x0a, 0x15, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0a, 0x61,
	0x6d, 0x6c, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0e, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x41, 0x6d, 0x6c, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x09, 0x61, 0x6d, 0x6c, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x3a,
	0x0a, 0x0b, 0x61, 0x6d, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x41, 0x6d, 0x6c, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x61, 0x6d, 0x6c, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x49, 0x0a, 0x10, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4d, 0x0a, 0x12, 0x61, 0x6d, 0x6c, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x41, 0x6d, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02,
	0x20, 0x00, 0x52, 0x10, 0x61, 0x6d, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x33, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x05, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x22, 0x86, 0x03, 0x0a, 0x16, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x05, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x61, 0x6d, 0x6c, 0x2e,
	0x41, 0x6d, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x05, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x12,
	0x2d, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x22, 0xf2,
	0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x2e, 0x0a, 0x26, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x43, 0x48, 0x41, 0x52, 0x41, 0x43, 0x54, 0x45, 0x52, 0x53, 0x10, 0x65, 0x1a, 0x02, 0x08,
	0x01, 0x12, 0x1a, 0x0a, 0x12, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x66, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x2d, 0x0a,
	0x25, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41,
	0x49, 0x4e, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x52,
	0x41, 0x43, 0x54, 0x45, 0x52, 0x53, 0x10, 0x67, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x2f, 0x0a, 0x27,
	0x4d, 0x49, 0x44, 0x44, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54,
	0x41, 0x49, 0x4e, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x48, 0x41,
	0x52, 0x41, 0x43, 0x54, 0x45, 0x52, 0x53, 0x10, 0x68, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x16, 0x0a,
	0x12, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x46, 0x4f, 0x52,
	0x4d, 0x41, 0x54, 0x10, 0x69, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52,
	0x54, 0x5f, 0x4c, 0x45, 0x4e, 0x47, 0x54, 0x48, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45,
	0x44, 0x10, 0x6a, 0x22, 0x81, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x33, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72,
	0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x22, 0xb6, 0x02, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x42, 0x0a, 0x10, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x41, 0x6d, 0x6c, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x23, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x46,
	0x6f, 0x75, 0x6e, 0x64, 0x12, 0x2d, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x05, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x41, 0x6d, 0x6c, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x05, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x36, 0x0a, 0x0d, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x11, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0xe7, 0x02, 0x0a, 0x10, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x12, 0x48, 0x0a, 0x0f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x43, 0x61,
	0x73, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0e, 0x63, 0x61, 0x73, 0x65,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x3a, 0x0a, 0x0a, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61,
	0x6d, 0x6c, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x61, 0x73,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x4a, 0x0a, 0x0e, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2,
	0x01, 0x02, 0x08, 0x01, 0x52, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x46, 0x0a, 0x0c, 0x74, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0a,
	0x74, 0x6f, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x38, 0x0a, 0x11, 0x53, 0x79,
	0x6e, 0x63, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x2a, 0x5c, 0x0a, 0x0d, 0x41, 0x6d, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x4d, 0x4c, 0x5f, 0x52, 0x45, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x4d, 0x4c, 0x5f, 0x52, 0x45,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x53, 0x53, 0x5f, 0x41, 0x4c,
	0x45, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x52, 0x35, 0x30, 0x38,
	0x10, 0x01, 0x32, 0x9b, 0x03, 0x0a, 0x03, 0x41, 0x6d, 0x6c, 0x12, 0x4b, 0x0a, 0x0e, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x1a, 0x2e, 0x61,
	0x6d, 0x6c, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x2e,
	0x61, 0x6d, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e,
	0x61, 0x6d, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x54, 0x0a, 0x11, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x47, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x2e,
	0x61, 0x6d, 0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x47, 0x65,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20,
	0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x47,
	0x65, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x3c, 0x0a, 0x09, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12,
	0x15, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x53, 0x79, 0x6e,
	0x63, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x42, 0x40, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x6d, 0x6c, 0x5a, 0x1e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6d, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_aml_service_proto_rawDescOnce sync.Once
	file_api_aml_service_proto_rawDescData = file_api_aml_service_proto_rawDesc
)

func file_api_aml_service_proto_rawDescGZIP() []byte {
	file_api_aml_service_proto_rawDescOnce.Do(func() {
		file_api_aml_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_aml_service_proto_rawDescData)
	})
	return file_api_aml_service_proto_rawDescData
}

var file_api_aml_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_aml_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_aml_service_proto_goTypes = []interface{}{
	(AmlReportType)(0),                  // 0: aml.AmlReportType
	(ScreenCustomerResponse_Status)(0),  // 1: aml.ScreenCustomerResponse.Status
	(*UpdateFileGenStatusRequest)(nil),  // 2: aml.UpdateFileGenStatusRequest
	(*UpdateFileGenStatusResponse)(nil), // 3: aml.UpdateFileGenStatusResponse
	(*ExtractReportDataRequest)(nil),    // 4: aml.ExtractReportDataRequest
	(*ExtractReportDataResponse)(nil),   // 5: aml.ExtractReportDataResponse
	(*ScreenCustomerRequest)(nil),       // 6: aml.ScreenCustomerRequest
	(*ScreenCustomerResponse)(nil),      // 7: aml.ScreenCustomerResponse
	(*GetScreeningStatusRequest)(nil),   // 8: aml.GetScreeningStatusRequest
	(*GetScreeningStatusResponse)(nil),  // 9: aml.GetScreeningStatusResponse
	(*SyncCasesRequest)(nil),            // 10: aml.SyncCasesRequest
	(*SyncCasesResponse)(nil),           // 11: aml.SyncCasesResponse
	(FileGenerationStatus)(0),           // 12: aml.FileGenerationStatus
	(*rpc.Status)(nil),                  // 13: rpc.Status
	(AmlEntity)(0),                      // 14: aml.AmlEntity
	(AmlProduct)(0),                     // 15: aml.AmlProduct
	(*CustomerDetails)(nil),             // 16: aml.CustomerDetails
	(AmlScreeningMode)(0),               // 17: aml.AmlScreeningMode
	(common.Owner)(0),                   // 18: api.typesv2.common.Owner
	(AmlMatch)(0),                       // 19: aml.AmlMatch
	(*MatchData)(nil),                   // 20: aml.MatchData
	(AmlScreeningStatus)(0),             // 21: aml.AmlScreeningStatus
	(ReviewStatus)(0),                   // 22: aml.ReviewStatus
	(aml.CaseCategory)(0),               // 23: vendorgateway.aml.CaseCategory
	(aml.CaseType)(0),                   // 24: vendorgateway.aml.CaseType
	(*timestamppb.Timestamp)(nil),       // 25: google.protobuf.Timestamp
}
var file_api_aml_service_proto_depIdxs = []int32{
	12, // 0: aml.UpdateFileGenStatusRequest.status:type_name -> aml.FileGenerationStatus
	13, // 1: aml.UpdateFileGenStatusResponse.status:type_name -> rpc.Status
	0,  // 2: aml.ExtractReportDataRequest.report_type:type_name -> aml.AmlReportType
	13, // 3: aml.ExtractReportDataResponse.status:type_name -> rpc.Status
	14, // 4: aml.ScreenCustomerRequest.aml_entity:type_name -> aml.AmlEntity
	15, // 5: aml.ScreenCustomerRequest.aml_product:type_name -> aml.AmlProduct
	16, // 6: aml.ScreenCustomerRequest.customer_details:type_name -> aml.CustomerDetails
	17, // 7: aml.ScreenCustomerRequest.aml_screening_mode:type_name -> aml.AmlScreeningMode
	18, // 8: aml.ScreenCustomerRequest.owner:type_name -> api.typesv2.common.Owner
	13, // 9: aml.ScreenCustomerResponse.status:type_name -> rpc.Status
	19, // 10: aml.ScreenCustomerResponse.match:type_name -> aml.AmlMatch
	20, // 11: aml.ScreenCustomerResponse.match_data:type_name -> aml.MatchData
	18, // 12: aml.GetScreeningStatusRequest.owner:type_name -> api.typesv2.common.Owner
	13, // 13: aml.GetScreeningStatusResponse.status:type_name -> rpc.Status
	21, // 14: aml.GetScreeningStatusResponse.screening_status:type_name -> aml.AmlScreeningStatus
	20, // 15: aml.GetScreeningStatusResponse.match_data:type_name -> aml.MatchData
	19, // 16: aml.GetScreeningStatusResponse.match:type_name -> aml.AmlMatch
	22, // 17: aml.GetScreeningStatusResponse.review_status:type_name -> aml.ReviewStatus
	18, // 18: aml.SyncCasesRequest.owner:type_name -> api.typesv2.common.Owner
	23, // 19: aml.SyncCasesRequest.case_categories:type_name -> vendorgateway.aml.CaseCategory
	24, // 20: aml.SyncCasesRequest.case_types:type_name -> vendorgateway.aml.CaseType
	25, // 21: aml.SyncCasesRequest.from_date_time:type_name -> google.protobuf.Timestamp
	25, // 22: aml.SyncCasesRequest.to_date_time:type_name -> google.protobuf.Timestamp
	13, // 23: aml.SyncCasesResponse.status:type_name -> rpc.Status
	6,  // 24: aml.Aml.ScreenCustomer:input_type -> aml.ScreenCustomerRequest
	8,  // 25: aml.Aml.GetScreeningStatus:input_type -> aml.GetScreeningStatusRequest
	4,  // 26: aml.Aml.ExtractReportData:input_type -> aml.ExtractReportDataRequest
	2,  // 27: aml.Aml.UpdateFileGenStatus:input_type -> aml.UpdateFileGenStatusRequest
	10, // 28: aml.Aml.SyncCases:input_type -> aml.SyncCasesRequest
	7,  // 29: aml.Aml.ScreenCustomer:output_type -> aml.ScreenCustomerResponse
	9,  // 30: aml.Aml.GetScreeningStatus:output_type -> aml.GetScreeningStatusResponse
	5,  // 31: aml.Aml.ExtractReportData:output_type -> aml.ExtractReportDataResponse
	3,  // 32: aml.Aml.UpdateFileGenStatus:output_type -> aml.UpdateFileGenStatusResponse
	11, // 33: aml.Aml.SyncCases:output_type -> aml.SyncCasesResponse
	29, // [29:34] is the sub-list for method output_type
	24, // [24:29] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_api_aml_service_proto_init() }
func file_api_aml_service_proto_init() {
	if File_api_aml_service_proto != nil {
		return
	}
	file_api_aml_aml_case_details_proto_init()
	file_api_aml_data_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_aml_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateFileGenStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_aml_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateFileGenStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_aml_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtractReportDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_aml_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtractReportDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_aml_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenCustomerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_aml_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenCustomerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_aml_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScreeningStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_aml_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScreeningStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_aml_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncCasesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_aml_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncCasesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_aml_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_aml_service_proto_goTypes,
		DependencyIndexes: file_api_aml_service_proto_depIdxs,
		EnumInfos:         file_api_aml_service_proto_enumTypes,
		MessageInfos:      file_api_aml_service_proto_msgTypes,
	}.Build()
	File_api_aml_service_proto = out.File
	file_api_aml_service_proto_rawDesc = nil
	file_api_aml_service_proto_goTypes = nil
	file_api_aml_service_proto_depIdxs = nil
}
