syntax = "proto3";

package aml;

import "api/aml/aml_case_details.proto";
import "api/aml/data.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/ownership.proto";
import "api/vendorgateway/aml/common.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/aml";
option java_package = "com.github.epifi.gamma.api.aml";

// aml service provides the APIs for screening users for anti money laundering
// aml service also stores the request and response for each aml screening request raised to the vendor
service Aml {
  // rpc to initiate AML screening or update existing user's data in AML system
  rpc ScreenCustomer (ScreenCustomerRequest) returns (ScreenCustomerResponse) {};
  // rpc to get the screening status and match details if matched
  rpc GetScreeningStatus (GetScreeningStatusRequest) returns (GetScreeningStatusResponse) {};
  // rpc to extract aml report data uploaded by agent on sherlock
  rpc ExtractReportData (ExtractReportDataRequest) returns (ExtractReportDataResponse) {};
  // rpc to update file generation status for a particular actor. This will be useful when
  // file generation fails for an actor, status can be updated to FILE_GENERATION_STATUS_RETRY_NEEDED to retry next time
  rpc UpdateFileGenStatus (UpdateFileGenStatusRequest) returns (UpdateFileGenStatusResponse) {};
  // SyncCases creates or updates cases from AML vendor to our case details table
  // This RPC may be called at periodic intervals to keep the case details table up to date.
  rpc SyncCases (SyncCasesRequest) returns (SyncCasesResponse) {};
}

message UpdateFileGenStatusRequest {
  // primary id of client id to actor id mapping
  string id = 1;
  // new file generation status
  FileGenerationStatus status = 2;
}

message UpdateFileGenStatusResponse {
  rpc.Status status = 1;
}

enum AmlReportType {
  AML_REPORT_TYPE_UNSPECIFIED = 0;
  // Report extracted from TSS vendor's portal containing the details regarding all the alerts
  AML_REPORT_TYPE_TSS_ALERT_DETAILS_R508 = 1;
}

message ExtractReportDataRequest {
  // type of the report
  AmlReportType report_type = 1;
  // binary data of the report
  bytes file_data = 2;
}

message ExtractReportDataResponse {
  rpc.Status status = 1;
}

message ScreenCustomerRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // deprecated since AML screening rules are configured based on AmlProduct and this is of no use
  AmlEntity aml_entity = 2 [deprecated = true];
  AmlProduct aml_product = 3 [(validate.rules).enum = {not_in: [0]}];
  CustomerDetails customer_details = 4 [(validate.rules).message.required = true];
  // In case of update mode, only the fields which need to be updated can be passed
  AmlScreeningMode aml_screening_mode = 5 [(validate.rules).enum = {not_in: [0]}];
  string client_request_id = 6 [(validate.rules).string.min_len = 1];

  // Owner of the screening request.
  // Required for adhering to data segregation compliance requirements of each regulatory body.
  api.typesv2.common.Owner owner = 7;
}

message ScreenCustomerResponse {
  enum Status {
    OK = 0;
    FIRST_NAME_CONTAINS_INVALID_CHARACTERS = 101 [deprecated = true];
    FIRST_NAME_MISSING = 102 [deprecated = true];
    LAST_NAME_CONTAINS_INVALID_CHARACTERS = 103 [deprecated = true];
    MIDDLE_NAME_CONTAINS_INVALID_CHARACTERS = 104 [deprecated = true];
    INVALID_PAN_FORMAT = 105;
    PASSPORT_LENGTH_EXCEEDED = 106;
  }
  rpc.Status status = 1;
  // match found in the screening attempt
  AmlMatch match = 2;
  // in case match is found, relevant data is populated here
  repeated MatchData match_data = 3;
}

message GetScreeningStatusRequest {
  string client_request_id = 1 [(validate.rules).string.min_len = 1];

  // Owner of the screening request.
  // Required for adhering to data segregation compliance requirements of each regulatory body.
  api.typesv2.common.Owner owner = 2;
}

message GetScreeningStatusResponse {
  rpc.Status status = 1;
  // status of the screening request
  AmlScreeningStatus screening_status = 2;
  // match found in the screening attempt
  bool match_found = 3 [deprecated = true];
  // in case match is found, relevant data is populated here
  repeated MatchData match_data = 4;
  // match found in the screening attempt
  AmlMatch match = 5;
  // Status of the review of the case opened against a screening attempt when a match is found
  ReviewStatus review_status = 6;
}

message SyncCasesRequest {
  // owner for which to sync cases
  api.typesv2.common.Owner owner = 1 [(validate.rules).enum.defined_only = true];
  // case categories to filter by. If not provided, all categories except unspecified will be used
  repeated vendorgateway.aml.CaseCategory case_categories = 2;
  // case types to filter by. If not provided, all types except unspecified will be used
  repeated vendorgateway.aml.CaseType case_types = 3;
  // start date for the sync range
  google.protobuf.Timestamp from_date_time = 4 [(validate.rules).timestamp.required = true];
  // end date for the sync range
  google.protobuf.Timestamp to_date_time = 5 [(validate.rules).timestamp.required = true];
}

message SyncCasesResponse {
  rpc.Status status = 1;
}
