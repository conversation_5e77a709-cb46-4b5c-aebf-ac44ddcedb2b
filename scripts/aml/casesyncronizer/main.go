package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"time"

	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	amlPb "github.com/epifi/gamma/api/aml"
	"github.com/epifi/gamma/aml"
)

var (
	fromDateStr = flag.String("from", "", "Start date for sync (format: 2006-01-02 or 2006-01-02T15:04:05Z07:00)")
	toDateStr   = flag.String("to", "", "End date for sync (format: 2006-01-02 or 2006-01-02T15:04:05Z07:00)")
)

const (
	dateOnlyLayout = "2006-01-02"
	dateTimeLayout = time.RFC3339
)

func main() {
	flag.Parse()

	// Set environment variables for configuration
	err := os.Setenv("ENVIRONMENT", "staging")
	if err != nil {
		fmt.Printf("Error setting environment: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger
	logger.Init(cfg.StagingEnv)
	defer func() { _ = logger.Log.Sync() }()

	ctx := epificontext.WithTraceId(context.Background(), metadata.New(map[string]string{}))

	// Parse date inputs
	fromTime, toTime, err := parseDateInputs(*fromDateStr, *toDateStr)
	if err != nil {
		logger.Error(ctx, "Error parsing date inputs", zap.Error(err))
		fmt.Printf("Error parsing dates: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("Syncing cases from %s to %s\n", fromTime.Format(time.RFC3339), toTime.Format(time.RFC3339))

	// Create AML service client
	amlConn := epifigrpc.NewConnByService(cfg.AML_SERVICE)
	defer epifigrpc.CloseConn(amlConn)
	amlClient := aml.NewAmlClient(amlConn)

	// Sync cases for both owners
	owners := []commontypes.Owner{
		commontypes.Owner_OWNER_EPIFI_TECH,
		commontypes.Owner_OWNER_STOCK_GUARDIAN_TSP,
	}

	for _, owner := range owners {
		ownerName := getOwnerName(owner)
		fmt.Printf("Syncing cases for owner: %s\n", ownerName)

		err := syncCasesForOwner(ctx, amlClient, owner, fromTime, toTime)
		if err != nil {
			logger.Error(ctx, "Error syncing cases for owner",
				zap.String("owner", ownerName),
				zap.Error(err))
			fmt.Printf("Error syncing cases for %s: %v\n", ownerName, err)
		} else {
			fmt.Printf("Successfully synced cases for %s\n", ownerName)
		}
	}

	fmt.Println("Case synchronization completed")
}

func parseDateInputs(fromStr, toStr string) (time.Time, time.Time, error) {
	var fromTime, toTime time.Time
	var err error

	if fromStr == "" || toStr == "" {
		// Default to last 30 days if not provided
		now := time.Now()
		toTime = now
		fromTime = now.AddDate(0, 0, -30)
		fmt.Println("No date range provided, using last 30 days")
		return fromTime, toTime, nil
	}

	// Try to parse as date-only first, then as full datetime
	fromTime, err = parseFlexibleDate(fromStr)
	if err != nil {
		return time.Time{}, time.Time{}, fmt.Errorf("invalid from date '%s': %w", fromStr, err)
	}

	toTime, err = parseFlexibleDate(toStr)
	if err != nil {
		return time.Time{}, time.Time{}, fmt.Errorf("invalid to date '%s': %w", toStr, err)
	}

	// Validate date range
	if fromTime.After(toTime) {
		return time.Time{}, time.Time{}, fmt.Errorf("from date cannot be after to date")
	}

	return fromTime, toTime, nil
}

func parseFlexibleDate(dateStr string) (time.Time, error) {
	// Try date-only format first
	if t, err := time.Parse(dateOnlyLayout, dateStr); err == nil {
		// If it's date-only, set to start of day
		return t, nil
	}

	// Try full datetime format
	if t, err := time.Parse(dateTimeLayout, dateStr); err == nil {
		return t, nil
	}

	return time.Time{}, fmt.Errorf("date must be in format YYYY-MM-DD or RFC3339 (e.g., 2006-01-02T15:04:05Z07:00)")
}

func syncCasesForOwner(ctx context.Context, client aml.AmlClient, owner commontypes.Owner, fromTime, toTime time.Time) error {
	req := &amlPb.SyncCasesRequest{
		Owner:        owner,
		FromDateTime: timestamppb.New(fromTime),
		ToDateTime:   timestamppb.New(toTime),
		// CaseCategories and CaseTypes will use defaults (all except unspecified) if not provided
	}

	resp, err := client.SyncCases(ctx, req)
	if err != nil {
		return fmt.Errorf("RPC call failed: %w", err)
	}

	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		return fmt.Errorf("RPC error: %w", rpcErr)
	}

	if !resp.GetStatus().IsOk() {
		return fmt.Errorf("sync failed with status: %s", resp.GetStatus().GetMessage())
	}

	return nil
}

func getOwnerName(owner commontypes.Owner) string {
	switch owner {
	case commontypes.Owner_OWNER_EPIFI_TECH:
		return "epifi-tech"
	case commontypes.Owner_OWNER_STOCK_GUARDIAN_TSP:
		return "sg"
	default:
		return "unknown"
	}
}
